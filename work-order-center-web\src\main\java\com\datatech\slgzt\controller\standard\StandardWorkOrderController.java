package com.datatech.slgzt.controller.standard;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.StandardWorkOrderAuditConvert;
import com.datatech.slgzt.convert.StandardWorkOrderWebConvert;
import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.MethodPathEnum;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.manager.WorkOrderAuthLogManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.order.GoodsProductDTO;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.req.standard.*;
import com.datatech.slgzt.model.resourcce.ResourceShowInfoDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.standard.StandardWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.standard.StandardWorkOrderVO;
import com.datatech.slgzt.service.ProductGeneralCheckService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.standard.StandardWorkOrderService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

/**
 * 标准工单管理
 *
 * @date 2025年 03月03日 18:19:03
 */
@RestController
@RequestMapping("/standardWorkorder")
public class StandardWorkOrderController {


    @Resource
    private StandardWorkOrderWebConvert convert;

    @Resource
    private StandardWorkOrderAuditConvert auditConvert;

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private StandardWorkOrderService standardWorkOrderService;

    @Resource
    private StandardWorkOrderProductManager standardWorkOrderProductManager;

    @Resource
    private ProductGeneralCheckService productGeneralCheckService;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private BaseActivity baseActivity;


    @Resource
    private UserHelper userHelper;

    /**
     * 非标流程草稿
     *
     * @param req 工单创建入参
     */
    @RequestMapping(value = "/draft", method = RequestMethod.POST)
    public CommonResult<String> draft(@RequestBody StandardWorkOrderCreateReq req) {
        //草稿只需要校验基础参数就直接创建流程但不推进
        Precondition.checkArgument(req.getOrderTitle(), "工单标题不能为空");
        StandardWorkOrderDTO dto = convert.convert(req);
        String standerWorkOrderId = standardWorkOrderManager.createNoStanderWorkOrder(new StandardWorkOrderDTO());
        dto.setId(standerWorkOrderId);
        standardWorkOrderService.draftSave(dto);
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(standerWorkOrderId);
        dto.setOrderCode(orderDTO.getOrderCode());
        String orderId = standardWorkOrderService.draft(dto, MethodPathEnum.DRAFTS_ORDER_OPEN.getPath());
        return CommonResult.success(orderId);
    }

    /**
     * 创建标准工单
     *
     * @param req 工单创建入参
     */
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(description = "创建标准工单", operationType = "CREATE")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public CommonResult<String> createNonStandardOrder(@RequestBody StandardWorkOrderCreateReq req) {
        preCheckWorkOrderReq(req);
        StandardWorkOrderDTO dto = convert.convert(req);
        String requestPath = MethodPathEnum.RESOURCE_OPEN.getPath();
        if (StringUtils.isNotEmpty(req.getId())) {
            // 重新提交
            requestPath = MethodPathEnum.RESOURCE_RESUBMIT_OPEN.getPath();
        } else {
            //预创建获取id
            String standerWorkOrderId = standardWorkOrderManager.createNoStanderWorkOrder(new StandardWorkOrderDTO());
            dto.setId(standerWorkOrderId);
        }
        //只要走创建的方法就先删除草稿，无所谓存不存在
        standardWorkOrderService.draftDel(UserHelper.INSTANCE.getCurrentUserId(), req.getId());
        processOrderProduct(dto);
        UserCenterUserDTO oacUserInfo = userHelper.getCurrentUser();
        standardWorkOrderService.createNonStandardOrderAndStartProcess(dto, requestPath, oacUserInfo);
        return CommonResult.success(dto.getId());
    }

    private void processOrderProduct(StandardWorkOrderDTO dto) {
        //创建前把所有product先删除
        String standerWorkOrderId = dto.getId();
        //清理资源
        standardWorkOrderProductManager.deleteByWorkOrderId(standerWorkOrderId);
        standardWorkOrderManager.clearSubmitProduct(dto.getId());
        //校验填充资源
        StreamUtils.mapArray(dto.getCpuEcsResourceList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillEscResource(item, standerWorkOrderId);
        });
        StreamUtils.mapArray(dto.getCloudEcsResourceList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillEscResource(item, standerWorkOrderId);
        });
        StreamUtils.mapArray(dto.getNatGatwayModelList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillNatResource(item, standerWorkOrderId);
        });
        StreamUtils.mapArray(dto.getSlbModelList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillSlbResource(item, standerWorkOrderId);
        });
        //这里使用原子引用是因为checkFillEvsResource方法会修改vmDomainCode的值
        AtomicReference<String> vmDomainCode = new AtomicReference<>();
        StreamUtils.mapArray(dto.getEvsModelList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillEvsResource(item, standerWorkOrderId,vmDomainCode);
            //vmDomainCode 可能会被上面的方法修改
            if (StringUtils.isNotEmpty(vmDomainCode.get())) {
                CatalogueDomain catalogueDomain = CatalogueDomain.getByCode(vmDomainCode.get());
                Precondition.checkArgument(catalogueDomain != null, "catalogueDomainCode不存在");
                dto.setDomainCode(catalogueDomain.getCode());
                dto.setDomainName(catalogueDomain.getName());
                dto.setCatalogueDomainCode(catalogueDomain.getParent().getCode());
                dto.setCatalogueDomainName(catalogueDomain.getParent().getName());
            }

        });
        StreamUtils.mapArray(dto.getEipModelList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillEipResource(item, standerWorkOrderId,vmDomainCode);
            //vmDomainCode 可能会被上面的方法修改
            if (StringUtils.isNotEmpty(vmDomainCode.get())) {
                CatalogueDomain catalogueDomain = CatalogueDomain.getByCode(vmDomainCode.get());
                Precondition.checkArgument(catalogueDomain != null, "catalogueDomainCode不存在");
                dto.setDomainCode(catalogueDomain.getCode());
                dto.setDomainName(catalogueDomain.getName());
                dto.setCatalogueDomainCode(catalogueDomain.getParent().getCode());
                dto.setCatalogueDomainName(catalogueDomain.getParent().getName());
            }
        });
        StreamUtils.mapArray(dto.getObsModelList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillObsResource(item, standerWorkOrderId);
        });
        StreamUtils.mapArray(dto.getCqModelList(), Function.identity()).forEach(item -> {
            standardWorkOrderService.checkFillCQResource(item, dto);
        });
    }

    /**
     * 标准工单流程审批
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @OperationLog(description = "标准工单审批", operationType = "UPDATE")
    public CommonResult<String> audit(@RequestBody @Validated StanderWorkOrderAuditReq req) {
        //判断当前节点是哪个如果是第一个节点需要额外
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        StandardAuditWorkOrderDTO auditDTO = auditConvert.req2DTO(req);
        standardWorkOrderService.audit(auditDTO);
        return CommonResult.success("操作成功");
    }

    /**
     * 撤销按钮逻辑
     */
    @RequestMapping(value = "/revokeButtonLogic", method = RequestMethod.POST)
    @OperationLog(description = "撤销标准工单", operationType = "UPDATE")
    public CommonResult<Boolean> revokeButtonLogic(@RequestBody StandardWorkOrderRevokeReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询工单
        StandardWorkOrderDTO dto = standardWorkOrderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        //获取当前用户
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        //判断当前用户是否是工单的创建者 并且工单到达 网络创建 和资源开通就不能撤了
        if(dto.getCreateUserId().equals(currentUser.getId())){
            //获取当前节点
            ActivityTaskVo taskNodes = baseActivity.taskNodes(dto.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
            //如果当前节点是网络创建或者资源开通就不能撤了
            if(taskNodes.getCurrentTask().equals(AuthorityCodeEnum.NETWORK_PROVISIONING.code())
                    || taskNodes.getCurrentTask().equals(AuthorityCodeEnum.RESOURCE_CREATION.code())){
                return CommonResult.success(false);
            }
        }
        return CommonResult.success(true);
    }

    /**
     * 标准工单存量校验接口
     */
    @RequestMapping(value = "/capacityCheck", method = RequestMethod.POST)
    public CommonResult<Set<String>> capacityCheck(@RequestBody StanderWorkOrderAuditReq req) {
        //查询工单
        StandardWorkOrderDTO dto = standardWorkOrderManager.getById(req.getOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        //如果domainCode是省内的话就不校验
        if (StringUtils.isNotEmpty(dto.getDomainCode()) && dto.getDomainCode().equals(CatalogueDomain.NETWORK_CLOUD_PROVINCE.getCode())) {
            return CommonResult.success(Sets.newHashSet());
        }
        List<GoodsProductDTO> goodsModelList = req.getGoodsModelList();
        //查询出所有的产品
        List<StandardWorkOrderProductDTO> productDTOS = standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery()
                .setOrderId(req.getOrderId())
                .setParentId(0L));
        //转换成校验用的Opm
        ProductGeneralCheckOpm opm = convert.convertCheckOpm(productDTOS, goodsModelList);
        opm.setCheckOpenNum(Boolean.FALSE);
        //如果省内就不校验
        if (CatalogueDomain.NETWORK_CLOUD_PROVINCE.getCode().equals(req.getDomainCode())) {
            return CommonResult.success(Sets.newHashSet());
        }
        //存量校验
        productGeneralCheckService.checkProductInResourcePool(opm);
        productGeneralCheckService.checkProductInResourcePoolCapacity(opm);
        return CommonResult.success(StreamUtils.toSet(opm.getErrorMessages()));
    }



    /**
     * 标准工单详情
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public CommonResult<StandardWorkOrderDetailVO> orderDetail(@RequestBody StandardWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询用户是否存在草稿缓存
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        StandardWorkOrderDTO draft = standardWorkOrderService.getDraft(currentUser.getId(),req.getWorkOrderId());
        if (draft != null) {
            //如果存在草稿缓存 就返回草稿
            return CommonResult.success(convert.convertDetailDraft(draft));
        }
        StandardWorkOrderDTO dto = standardWorkOrderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        Precondition.checkArgument(dto.getActivitiId(), "工单异常不存在流程id");
        StandardWorkOrderDetailVO vo = convert.convertDetail(dto);
        //获取资源的产品详情
        List<StandardWorkOrderProductDTO> productDTOS = standardWorkOrderProductManager
                .list(new StandardWorkOrderProductQuery().setOrderId(req.getWorkOrderId()));
        convert.fillDetail(vo, productDTOS,req.getAggregation());
        //获取审批记录
        List<WorkOrderAuthLogDTO> authLogDTOS = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(req.getWorkOrderId()));
        ActivityTaskVo activityTaskVo = standardWorkOrderService.getTaskNodes(dto.getId());
        standardWorkOrderService.getNextTaskNodes(dto.getId(),authLogDTOS,activityTaskVo.getCurrentTaskName());
        vo.setActivityTask(activityTaskVo);
        vo.setApproveRecordList(authLogDTOS);
        // 获取资源概览
        ResourceShowInfoDTO showInfoDTO = standardWorkOrderProductManager.selectResourceOverview(new StandardWorkOrderProductQuery().setOrderId(req.getWorkOrderId()));
        vo.setShowInfo(showInfoDTO);

        return CommonResult.success(vo);
    }

    /**
     * 分页查询
     * 需要根据条件判断当前查询的是待审批 还是已审批 还是驳回的工单
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<StandardWorkOrderVO>> page(@RequestBody StandardWorkOrderPageReq req) {
        StandardWorkOrderQuery query = convert.convert(req);
        //判断查询的是待审批 还是已审批 还是驳回的工单
        PageResult<StandardWorkOrderDTO> page = standardWorkOrderService.page(query, UserHelper.INSTANCE.getCurrentUserId());
        PageResult<StandardWorkOrderVO> box = PageWarppers.box(page, convert::convert);
        List<StandardWorkOrderVO> records = box.getRecords();
        //获取当前用户
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        records.forEach(item -> {
            //判断当前用户是否是工单的创建者 并且工单到达 网络创建 和资源开通就不能撤了
            if(!item.getCreatedBy().equals(currentUser.getId())){
                item.setCanRevoke(false);
                return;
            }
                //获取当前节点
            ActivityTaskVo taskNodes = baseActivity.taskNodes(item.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
                //如果当前节点是网络创建或者资源开通就不能撤了
            if (null==taskNodes.getCurrentTask()) {
                item.setCanRevoke(false);
            }
            if ("autodit end".equals(taskNodes.getCurrentTask())) {
                item.setCanRevoke(false);
            }
            if(taskNodes.getCurrentTask().equals(AuthorityCodeEnum.NETWORK_PROVISIONING.code())
                        || taskNodes.getCurrentTask().equals(AuthorityCodeEnum.RESOURCE_CREATION.code())){
                item.setCanRevoke(false);
            }
        });
        return CommonResult.success(box);
    }

    /**
     * 撤销
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    @OperationLog(description = "撤销标准工单", operationType = "CREATE")
    public CommonResult<Void> revoke(@RequestBody StandardWorkOrderCancelReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //判断当前用户是不是工单的创建者
        standardWorkOrderService.cancel(req.getWorkOrderId());
        return CommonResult.success(null);
    }

    @RequestMapping(value = "/auditCount", method = RequestMethod.POST)
    public CommonResult<AuditCountVo> auditCount(@RequestBody StandardWorkOrderQuery orderQuery) {
        Long userId = userHelper.getCurrentUserId();
        Precondition.checkArgument(userId, "获取当前用户信息失败");
        orderQuery.setUserId(String.valueOf(userId));
        AuditCountVo countVo = standardWorkOrderService.orderCountCount(orderQuery, userId);
        return CommonResult.success(countVo);
    }

    private void preCheckWorkOrderReq(StandardWorkOrderCreateReq req) {
        Precondition.checkArgument(req.getBusiSystemId(), "业务系统不能为空");
        Precondition.checkArgument(req.getModuleId(), "业务系统模块不能为空");
        Precondition.checkArgument(req.getBusiDepartLeaderId(), "二级业务部门领导ID不能为空");
        Precondition.checkArgument(req.getLevelThreeLeaderId(), "三级业务部门领导ID不能为空");
        Precondition.checkArgument(req.getResourceApplyFiles(), "资源上云说明书附件不能为空");
        Precondition.checkArgument(req.getOrderTitle(), "title不能为空");
        Precondition.checkArgument(req.getOrderDesc(), "orderDesc不能为空");
        //Precondition.checkArgument(req.getResourceApplyFiles(), "resourceApplyFile不能为空");
    }
}
