package com.datatech.slgzt.impl.service.xieyun;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.manager.xieyun.*;
import com.datatech.slgzt.model.dto.xieyun.QuotaResultDTO;
import com.datatech.slgzt.model.xieyun.*;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.job.flow.FlowExecutionStatus;
import org.springframework.batch.core.job.flow.JobExecutionDecider;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

import javax.annotation.Resource;

@Configuration
public class XieyunEnvironmentCreateJobDef {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;
    @Resource
    private XieyunEnvironmentCreateJobListener environmentJobCompletionListener;
    @Resource
    private XieyunOrgManager xieyunOrgManager;
    @Resource
    private XieyunProjectManager xieyunProjectManager;
    @Resource
    private XieyunNamespacesManager xieyunNamespacesManager;
    @Resource
    private XieyunUserManager xieyunUserManager;
    @Resource
    private XieyunRepoManager xieyunRepoManager;


    @Bean("xieyun_env_createJob")
    public Job environmentCreationJob() {
        Job job=jobBuilderFactory.get("xieyun_env_createJob").incrementer(new RunIdIncrementer())
                .listener(environmentJobCompletionListener)
                //创建用户，如果存在则返回用户id，不存在则创建用户
                .start(ensureUserStep())
                //创建组织，如果存在则返回组织id，不存在则创建组织
                .next(ensureOrgStep())
                //用户加入组织
                .next(userJoinOrgStep())
                //组织分配资源
                .next(orgQuotaStep())
                //创建项目，如果存在则返回项目id，不存在则创建项目
                .next(ensureProjectStep())
                // 分配资源
                .next(projectQuotaStep())
                // //创建命名空间
                // .next(createNamespaceStep())
//                //命名空间网络配置
//                .next(namespaceNetworkStep())
//                //创建仓库
//                .next(createRepoStep())
//                //仓库分配组织
//                .next(repoAssignOrg())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //用户创建，如果存在则返回用户id，不存在则创建用户
    @Bean("xieyun_env_user_ensure")
    public Step ensureUserStep() {
        return stepBuilderFactory.get("xieyun_env_user_ensure").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            String createUserName = chunkContext.getStepContext().getJobParameters().get("createUserName").toString();
            String name = chunkContext.getStepContext().getJobParameters().get("name").toString();
            String mobile = chunkContext.getStepContext().getJobParameters().get("mobile").toString();
            String email = chunkContext.getStepContext().getJobParameters().get("email").toString();
            // 创建用户
            XieYunUserCreateOpm xieyunUserCreateOpm = new XieYunUserCreateOpm();
            xieyunUserCreateOpm.setUsername(createUserName);
            xieyunUserCreateOpm.setName(name);
            xieyunUserCreateOpm.setMobile(mobile);
            xieyunUserCreateOpm.setEmail(email);
            String userId = xieyunUserManager.ensureUser(xieyunUserCreateOpm);
            // 将userId存入
            chunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext().put("userId", userId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    //创建组织
    @Bean("xieyun_env_org_ensure")
    public Step ensureOrgStep() {
        return stepBuilderFactory.get("xieyun_env_org_ensure").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            // 获取参数
            String orgName = jobParamHelper.require("orgName");
            String orgDesc = jobParamHelper.require("orgDesc");
            // 创建组织
            XieyunOrgCreateOpm xieyunOrgCreateOpm = new XieyunOrgCreateOpm();
            xieyunOrgCreateOpm.setCode(IdUtil.nanoId());
            xieyunOrgCreateOpm.setName(orgName);
            xieyunOrgCreateOpm.setDescription(orgDesc);
            String orgId = xieyunOrgManager.ensureOrg(xieyunOrgCreateOpm);
            // 保存orgId供后续使用
            jobParamHelper.put("orgId", orgId);
            return RepeatStatus.FINISHED;
        }).build();
    }
    //分配用户
    @Bean("xieyun_env_user_join_org")
    public Step userJoinOrgStep() {
        return stepBuilderFactory.get("xieyun_env_user_join_org").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            // 获取参数
            String orgId = jobParamHelper.require("orgId");
            String userId = jobParamHelper.require("userId");
            // 分配用户
            xieyunUserManager.userJoinOrg(orgId, userId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    //分配资源
    @Bean("xieyun_env_org_quota")
    public Step orgQuotaStep() {
        return stepBuilderFactory.get("xieyun_env_org_quota").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            // 获取参数
            String orgId = jobParamHelper.require("orgId");
            String clusterName = jobParamHelper.require("clusterName");
            String nodePoolName = jobParamHelper.require("nodePoolName");
            
            String cpuValue = jobParamHelper.require("cpuValue");
            String memoryValue = jobParamHelper.require("memoryValue");
            String gpuCore = jobParamHelper.require("gpuCore");
            String gpuRatio = jobParamHelper.require("gpuRatio");
            String gpuVirtualCore = jobParamHelper.require("gpuVirtualCore");
            String gpuVirtualMemory = jobParamHelper.require("gpuVirtualMemory");
            // 转换成浮点数
            BigDecimal cpuValueBD = new BigDecimal(cpuValue);
            BigDecimal memoryValueBD = new BigDecimal(memoryValue);
            BigDecimal gpuCoreBD = new BigDecimal(gpuCore);
            BigDecimal gpuRatioBD = new BigDecimal(gpuRatio);
            BigDecimal gpuVirtualCoreBD = new BigDecimal(gpuVirtualCore);
            BigDecimal gpuVirtualMemoryBD = new BigDecimal(gpuVirtualMemory);

            // 先查询
            QuotaResultDTO quotaResultDTO = xieyunOrgManager.selectOrgQuota(clusterName, nodePoolName, orgId);
            if (cpuValueBD
                    .compareTo(quotaResultDTO.getCpu().getMax().subtract(quotaResultDTO.getCpu().getQuota())) > 0) {
                throw new RuntimeException("CPU资源不足");
            }
            if (memoryValueBD.compareTo(
                    quotaResultDTO.getMemory().getMax().subtract(quotaResultDTO.getMemory().getQuota())) > 0) {
                throw new RuntimeException("内存资源不足");
            }
            if (gpuCoreBD.compareTo(
                    quotaResultDTO.getGpuCore().getMax().subtract(quotaResultDTO.getGpuCore().getQuota())) > 0) {
                throw new RuntimeException("GPU核心资源不足");
            }
            if (gpuRatioBD.compareTo(
                    quotaResultDTO.getGpuRatio().getMax().subtract(quotaResultDTO.getGpuRatio().getQuota())) > 0) {
                throw new RuntimeException("GPU算力资源不足");
            }
            if (gpuVirtualCoreBD.compareTo(quotaResultDTO.getGpuVirtualCore().getMax()
                    .subtract(quotaResultDTO.getGpuVirtualCore().getQuota())) > 0) {
                throw new RuntimeException("GPU虚拟核心资源不足");
            }
            if (gpuVirtualMemoryBD.compareTo(quotaResultDTO.getGpuVirtualMemory().getMax()
                    .subtract(quotaResultDTO.getGpuVirtualMemory().getQuota())) > 0) {
                throw new RuntimeException("GPU虚拟内存资源不足");
            }

            // 分配资源
            XieyunOrgQuotaOpm xieyunOrgQuotaOpm = new XieyunOrgQuotaOpm();
            xieyunOrgQuotaOpm.setCpuValue(cpuValueBD.add(quotaResultDTO.getCpu().getQuota()).toString());
            xieyunOrgQuotaOpm.setMemoryValue(memoryValueBD.add(quotaResultDTO.getMemory().getQuota()).toString());
            xieyunOrgQuotaOpm.setGpuCoreValue(gpuCoreBD.add(quotaResultDTO.getGpuCore().getQuota()).toString());
            xieyunOrgQuotaOpm.setGpuRatioValue(gpuRatioBD.add(quotaResultDTO.getGpuRatio().getQuota()).toString());
            xieyunOrgQuotaOpm.setGpuVirtualCoreValue(gpuVirtualCoreBD.add(quotaResultDTO.getGpuVirtualCore().getQuota()).toString());
            xieyunOrgQuotaOpm.setGpuVirtualMemoryValue(gpuVirtualMemoryBD.add(quotaResultDTO.getGpuVirtualMemory().getQuota()).toString());
            xieyunOrgManager.orgQuota(clusterName, nodePoolName, orgId, xieyunOrgQuotaOpm);
            return RepeatStatus.FINISHED;
        }).build();
    }


    //创建项目，如果存在则返回项目id，不存在则创建项目
    @Bean("xieyun_env_project_ensure")
    public Step ensureProjectStep() {
        return stepBuilderFactory.get("xieyun_env_project_ensure").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            // 获取参数
            String projectName = jobParamHelper.require("projectName");
            String projectDesc = jobParamHelper.require("projectDesc");
            String orgId = jobParamHelper.require("orgId");
            // 创建项目
            XieyunProjectCreateOpm xieyunProjectCreateOpm = new XieyunProjectCreateOpm();
            xieyunProjectCreateOpm.setProjectName(projectName);
            xieyunProjectCreateOpm.setDescription(projectDesc);
            xieyunProjectCreateOpm.setOrgId(orgId);
            xieyunProjectManager.queryProject(projectName);
            String projectId = xieyunProjectManager.ensureProject(orgId, xieyunProjectCreateOpm);
            // 保存projectId供后续使用
            jobParamHelper.put("projectId", projectId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    //分配资源
    @Bean("xieyun_env_project_quota")
    public Step projectQuotaStep() {
        return stepBuilderFactory.get("xieyun_env_project_quota").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            // 获取参数
            String projectId = jobParamHelper.require("projectId");
            String clusterName = jobParamHelper.require("clusterName");
            String nodePoolName = jobParamHelper.require("nodePoolName");
            String cpuValue = jobParamHelper.require("cpuValue");
            String memoryValue = jobParamHelper.require("memoryValue");
            String orgId = jobParamHelper.require("orgId");
            String gpuCore = jobParamHelper.require("gpuCore");
            String gpuRatio = jobParamHelper.require("gpuRatio");
            String gpuVirtualCore = jobParamHelper.require("gpuVirtualCore");
            String gpuVirtualMemory = jobParamHelper.require("gpuVirtualMemory");
            // 转换成浮点数
            BigDecimal cpuValueBD = new BigDecimal(cpuValue);
            BigDecimal memoryValueBD = new BigDecimal(memoryValue);
            BigDecimal gpuCoreBD = new BigDecimal(gpuCore);
            BigDecimal gpuRatioBD = new BigDecimal(gpuRatio);
            BigDecimal gpuVirtualCoreBD = new BigDecimal(gpuVirtualCore);
            BigDecimal gpuVirtualMemoryBD = new BigDecimal(gpuVirtualMemory);

            // 先查询
            QuotaResultDTO quotaResultDTO = xieyunProjectManager.selectProjectQuota(clusterName, nodePoolName, orgId, projectId);
            if (cpuValueBD
                    .compareTo(quotaResultDTO.getCpu().getMax().subtract(quotaResultDTO.getCpu().getQuota())) > 0) {
                throw new RuntimeException("CPU资源不足");
            }
            if (memoryValueBD.compareTo(
                    quotaResultDTO.getMemory().getMax().subtract(quotaResultDTO.getMemory().getQuota())) > 0) {
                throw new RuntimeException("内存资源不足");
            }
            if (gpuCoreBD.compareTo(
                    quotaResultDTO.getGpuCore().getMax().subtract(quotaResultDTO.getGpuCore().getQuota())) > 0) {
                throw new RuntimeException("GPU核心资源不足");
            }
            if (gpuRatioBD.compareTo(
                    quotaResultDTO.getGpuRatio().getMax().subtract(quotaResultDTO.getGpuRatio().getQuota())) > 0) {
                throw new RuntimeException("GPU算力资源不足");
            }
            if (gpuVirtualCoreBD.compareTo(quotaResultDTO.getGpuVirtualCore().getMax()
                    .subtract(quotaResultDTO.getGpuVirtualCore().getQuota())) > 0) {
                throw new RuntimeException("GPU虚拟核心资源不足");
            }
            if (gpuVirtualMemoryBD.compareTo(quotaResultDTO.getGpuVirtualMemory().getMax()
                    .subtract(quotaResultDTO.getGpuVirtualMemory().getQuota())) > 0) {
                throw new RuntimeException("GPU虚拟内存资源不足");
            }

            // 分配资源
            XieyunProjectQuotaOpm xieyunProjectQuotaOpm = new XieyunProjectQuotaOpm();
            xieyunProjectQuotaOpm.setCpuValue(cpuValueBD.add(quotaResultDTO.getCpu().getQuota()).toString());
            xieyunProjectQuotaOpm.setMemoryValue(memoryValueBD.add(quotaResultDTO.getMemory().getQuota()).toString());
            xieyunProjectQuotaOpm.setGpuCoreValue(gpuCoreBD.add(quotaResultDTO.getGpuCore().getQuota()).toString());
            xieyunProjectQuotaOpm.setGpuRatioValue(gpuRatioBD.add(quotaResultDTO.getGpuRatio().getQuota()).toString());
            xieyunProjectQuotaOpm.setGpuVirtualCoreValue(gpuVirtualCoreBD.add(quotaResultDTO.getGpuVirtualCore().getQuota()).toString());
            xieyunProjectQuotaOpm.setGpuVirtualMemoryValue(gpuVirtualMemoryBD.add(quotaResultDTO.getGpuVirtualMemory().getQuota()).toString());
            xieyunProjectManager.projectQuota(orgId,projectId, clusterName, nodePoolName, xieyunProjectQuotaOpm);
            return RepeatStatus.FINISHED;
        }).build();
    }


    //命名空间
    @Bean("xieyun_env_namespace_natwork_create")
    public Step createNamespaceStep() {
        return stepBuilderFactory.get("xieyun_env_namespace_natwork_create").tasklet((stepContribution, chunkContext) -> {
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            String namespaceName=jobParamHelper.require("namespaceName");
            String namespaceDesc=jobParamHelper.require("namespaceDesc");
            String clusterName=jobParamHelper.require("clusterName");
            String nodePoolName=jobParamHelper.require("nodePoolName");
            String cpuValue=jobParamHelper.require("cpuValue");
            String memoryValue=jobParamHelper.require("memoryValue");
            String orgId=jobParamHelper.require("orgId");
            String projectId=jobParamHelper.require("projectId");
            String gpuRatio=jobParamHelper.require("gpuRatio");
            String gpuVirtualMemory=jobParamHelper.require("gpuVirtualMemory");
            String gpuCore=jobParamHelper.require("gpuCore");
            String gpuVirtualCore=jobParamHelper.require("gpuVirtualCore");
            
            // 创建命名空间
            XieyunNamespaceCreateOpm xieyunNamespaceCreateOpm = new XieyunNamespaceCreateOpm();
            xieyunNamespaceCreateOpm.setName(namespaceName);
            xieyunNamespaceCreateOpm.setDescription(namespaceDesc);
            xieyunNamespaceCreateOpm.setCpu(cpuValue);
            xieyunNamespaceCreateOpm.setMemory(memoryValue);
            xieyunNamespaceCreateOpm.setGpuCore(gpuCore);
            xieyunNamespaceCreateOpm.setGpuRatio(gpuRatio);
            xieyunNamespaceCreateOpm.setGpuVirtualCore(gpuVirtualCore);
            xieyunNamespaceCreateOpm.setGpuVirtualMemory(gpuVirtualMemory);
            xieyunNamespaceCreateOpm.setClusterName(clusterName);
            xieyunNamespaceCreateOpm.setNodePoolName(nodePoolName);
            String namespaceId = xieyunNamespacesManager.createNamespace(orgId, projectId, clusterName, xieyunNamespaceCreateOpm);
            // 保存projectId供后续使用
            jobParamHelper.put("namespaceId", namespaceId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    //分配网络
    @Bean("xieyun_env_namespace_natwork_config")
    public Step namespaceNetworkStep() {
        return stepBuilderFactory.get("xieyun_env_namespace_natwork_config").tasklet((stepContribution, chunkContext) -> {
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            String namespaceName=jobParamHelper.require("namespaceName");
            String namespaceId=jobParamHelper.require("namespaceId");
            String clusterName=jobParamHelper.require("clusterName");
            String orgId=jobParamHelper.require("orgId");
            String projectId=jobParamHelper.require("projectId");
            // 分配网络
            xieyunNamespacesManager.configNamespaceNetwork(namespaceId,orgId, projectId, clusterName, CollectionUtil.toList(namespaceName));
            return RepeatStatus.FINISHED;
        }).build();
    }

    //创建镜像仓库
    @Bean("xieyun_env_repo_create")
    public Step createRepoStep() {
        return stepBuilderFactory.get("xieyun_env_repo_create").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            String repoName=jobParamHelper.require("repoName");
            String registryId=jobParamHelper.require("registryId");
            // 创建镜像仓库,// 制品表id：registryId
            String repoId = xieyunRepoManager.createRepo(registryId, repoName);
            jobParamHelper.put("repoId", repoId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    //分配组织
    @Bean("xieyun_env_repo_assignOrg")
    public Step repoAssignOrg() {
        return stepBuilderFactory.get("xieyun_env_repo_assignOrg").tasklet((stepContribution, chunkContext) -> {
            // 获取参数
            // 获取参数
            JobStepHelper jobParamHelper = new JobStepHelper(chunkContext);
            String repoId=jobParamHelper.require("repoId");
            String registryId=jobParamHelper.require("registryId");
            String orgId=jobParamHelper.require("orgId");
            // 分配组织
            xieyunRepoManager.assignOrg(registryId, orgId, repoId);
            return RepeatStatus.FINISHED;
        }).build();
    }
}