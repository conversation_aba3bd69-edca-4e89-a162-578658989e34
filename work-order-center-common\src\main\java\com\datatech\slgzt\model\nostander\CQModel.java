package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 容器资源配额模型
 * <AUTHOR>
 * @description 容器资源配额配合开通模型
 * @date 2025年05月27日
 */
@Data
public class CQModel extends BaseProductModel {

    /**
     * 配合名称（必填）
     */
    private String cqName;

    /**
     * 容器配额-核心数（必填）
     */
    @JsonProperty("vCpus")
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G（必填）
     * 类似4核8G中的8G
     */
    private Integer ram;

    /**
     * GPU算力(算力)（非必填）
     */
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB（非必填）
     */
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)（非必填）
     */
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)（非必填）
     */
    private Integer gpuVirtualCore;

    /**
     * 4A账号(必填)
     */
    private String a4Account;

    /**
     * 4A账号绑定的手机(必填)
     */
    private String a4Phone;

    /**
     * 申请时长（必填）
     */
    private String applyTime;

    /**
     * 开通数量
     */
    private Integer openNum;

    /**
     * 云类型
     */
    private String catalogueDomainCode;

    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云平台id
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    // ==================== 协云平台相关参数 ====================

    // /**
    //  * 协云用户名（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的createUserName
    //  */
    // private String xieyunUserName;

    // /**
    //  * 协云用户显示名称（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的name
    //  */
    // private String xieyunDisplayName;

    // /**
    //  * 协云用户邮箱（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的email
    //  */
    // private String xieyunEmail;

    // /**
    //  * 协云组织名称（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的orgName
    //  */
    // private String xieyunOrgName;

    // /**
    //  * 协云组织描述（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的orgDesc
    //  */
    // private String xieyunOrgDesc;

    // /**
    //  * 协云集群名称（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的clusterName
    //  * 默认值：eic-wz-cluster
    //  */
    // private String xieyunClusterName;

    // /**
    //  * 协云节点池名称（必填）
    //  * 对应XieyunEnvironmentServiceImpl中的nodePoolName
    //  * 默认值：default
    //  */
    // private String xieyunNodePoolName;

    // /**
    //  * 协云项目名称（可选）
    //  * 对应XieyunEnvironmentServiceImpl中的projectName
    //  */
    // private String xieyunProjectName;

    // /**
    //  * 协云项目描述（可选）
    //  * 对应XieyunEnvironmentServiceImpl中的projectDesc
    //  */
    // private String xieyunProjectDesc;

    // /**
    //  * 协云命名空间名称（可选）
    //  * 对应XieyunEnvironmentServiceImpl中的namespaceName
    //  */
    // private String xieyunNamespaceName;

    // /**
    //  * 协云命名空间描述（可选）
    //  * 对应XieyunEnvironmentServiceImpl中的namespaceDesc
    //  */
    // private String xieyunNamespaceDesc;

    // /**
    //  * 协云仓库名称（可选）
    //  * 对应XieyunEnvironmentServiceImpl中的repoName
    //  */
    // private String xieyunRepoName;

    // /**
    //  * 协云制品ID（可选）
    //  * 对应XieyunEnvironmentServiceImpl中的registryId
    //  * 默认值：4940087269a6443b
    //  */
    // private String xieyunRegistryId;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

}
