package com.datatech.slgzt.impl.service.xieyun;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.service.container.ContainerQuotaService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class XieyunEnvironmentCreateJobListener implements JobExecutionListener {

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private ContainerQuotaService containerQuotaService;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        // 可以在这里添加一些初始化操作，比如设置默认参数等
        log.info("协云环境创建Job即将执行: {}", jobExecution.getJobInstance().getJobName());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        JobParameters jobParameters = jobExecution.getJobParameters();
        Long subOrderId = jobParameters.getLong("subOrderId");

        if (jobExecution.getStatus() == BatchStatus.FAILED) {
            log.error("协云环境创建Job执行失败，子订单ID: {}", subOrderId);
            // 获取失败异常
            List<Throwable> exceptions = jobExecution.getAllFailureExceptions();
            String errorMsg = exceptions.stream()
                    .map(Throwable::getMessage)
                    .collect(Collectors.joining(", "));
            StandardWorkOrderProductDTO productDTO = productManager.getBySubOrderId(subOrderId);
            if (productDTO != null) {
                productDTO.setMessage(errorMsg);
                productManager.update(productDTO);
            }
            // 更新产品状态为开通失败，并更新父产品状态为开通失败
            productManager.updateStatusById(subOrderId, ResOpenEnum.OPEN_FAIL.getCode());
            productManager.updateStatusByParentId(subOrderId, ResOpenEnum.OPEN_FAIL.getCode());
            // 可以发送告警邮件/短信等
            log.error("Job执行失败详情: {}", errorMsg);
        } else if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
            log.info("协云环境创建Job执行成功，子订单ID: {}", subOrderId);

            try {
                // 插入容器配额记录
                insertContainerQuotaRecord(subOrderId, jobParameters);

                // 更新产品状态为开通成功，并更新父产品状态为开通成功
                productManager.updateStatusById(subOrderId, ResOpenEnum.OPEN_SUCCESS.getCode());
                productManager.updateStatusByParentId(subOrderId, ResOpenEnum.OPEN_SUCCESS.getCode());

            } catch (Exception e) {
                log.error("Job执行成功后处理容器配额记录失败，子订单ID: {}", subOrderId, e);
            }
        } else {
            log.info("协云环境创建Job执行中，子订单ID: {}，状态: {}", subOrderId, jobExecution.getStatus());
        }
    }

    /**
     * 插入容器配额记录
     *
     * @param subOrderId    子订单ID
     * @param jobParameters Job参数
     */
    private void insertContainerQuotaRecord(Long subOrderId, JobParameters jobParameters) {
        try {
            log.info("开始插入容器配额记录，子订单ID: {}", subOrderId);

            // 根据子订单ID获取产品信息
            StandardWorkOrderProductDTO productDTO = productManager.getBySubOrderId(subOrderId);
            if (productDTO == null) {
                log.warn("未找到子订单对应的产品信息，子订单ID: {}", subOrderId);
                return;
            }

            // 从产品快照中解析CQModel
            String propertySnapshot = productDTO.getPropertySnapshot();
            if (propertySnapshot == null || propertySnapshot.trim().isEmpty()) {
                log.warn("产品快照为空，无法解析CQModel，子订单ID: {}", subOrderId);
                return;
            }

            CQModel cqModel = JSON.parseObject(propertySnapshot, CQModel.class);
            if (cqModel == null) {
                log.warn("解析CQModel失败，子订单ID: {}", subOrderId);
                return;
            }

            // 从JobParameters中获取额外信息（如果需要的话）
            String orgName = jobParameters.getString("orgName");
            if (orgName != null && !orgName.trim().isEmpty()) {
                cqModel.setCqName(orgName);
            }

            // 调用容器配额服务创建记录（CQModel中已包含业务系统信息）
            containerQuotaService.createContainerQuota(
                    cqModel,
                    productDTO.getWorkOrderId(),
                    subOrderId.toString());

            log.info("容器配额记录插入成功，子订单ID: {}, 工单ID: {}", subOrderId, productDTO.getWorkOrderId());

        } catch (Exception e) {
            log.error("插入容器配额记录失败，子订单ID: {}", subOrderId, e);
            throw e;
        }
    }
}